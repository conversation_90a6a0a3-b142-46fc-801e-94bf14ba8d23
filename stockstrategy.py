#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Stock Strategy Trading Bot
--------------------------
This bot implements a stock trading strategy based on option chain data analysis.

Strategy Rules:
1. Filter stocks every 20 minutes based on:
   - LTP near max_put_oi_strike (within 5% range)
   - LTP > 100
   - 7% of Max_Put_OI_Strike + Max_Put_OI_Strike >= Max_Call_OI_Strike
2. Monitor filtered stocks every second for live LTP updates
3. Use max_put_oi_strike as support and max_call_oi_strike as resistance
4. Buy when LTP crosses support + 0.5% buffer from below to above
5. Investment amount: 100,000 per trade
6. Stop Loss: 1.5% below buying price
7. Target: When LTP crosses below resistance - 1% buffer
8. Auto square-off all positions at month end
"""

import os
import json
import copy
import pandas as pd
import numpy as np
from kiteconnect import KiteConnect, KiteTicker
import time
import dateutil.parser
import threading
import sys
import requests
import urllib
import urllib.parse
import datetime
import configparser
import pymysql
import logging
from typing import Dict, List, Optional, Tuple
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_strategy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Global variables
INVESTMENT_AMOUNT = 100000  # Investment amount per trade
BUFFER_PERCENTAGE = 0.5     # Buffer percentage for support/resistance
SL_PERCENTAGE = 1.5         # Stop loss percentage
TARGET_BUFFER = 1.0         # Target buffer percentage
FILTER_UPDATE_INTERVAL = 20 * 60  # 20 minutes in seconds
FILTER_LIST_UPDATE_INTERVAL = 10 * 60  # 10 minutes in seconds

def send_telegram_message(message):
    """Send message to Telegram"""
    try:
        encoded_message = urllib.parse.quote(message)
        base_url = f"https://api.telegram.org/bot**********************************************/sendMessage?chat_id=-4735843712&text={encoded_message}"
        # base_url1 = f"https://api.telegram.org/**********************************************/sendMessage?chat_id=-4735843712&text={encoded_message}"
        response = requests.get(base_url)
        # response = requests.get(base_url1)
    except Exception as e:
        logger.error(f"Error sending telegram message: {e}")

class StockStrategy:
    def __init__(self):
        """Initialize the Stock Strategy Bot"""
        self.db_config = self._get_db_config()
        self.conn = None
        self.cursor = None
        self._connect_to_db()
        
        # Initialize trading variables
        self.filtered_stocks = []
        self.last_filter_time = 0
        self.last_filter_list_update = 0
        
        # Previous price tracking for crossover detection
        self.prev_ltp_data = {}
        self.price_history = {}  # Store last few prices for better crossover detection
        
        # Kite Connect variables
        self.login_credential = None
        self.access_token = None
        self.kite = None
        self.kws = None
        self.user_id = None
        
        # Live data tracking
        self.tick_data = {}
        self.token_symbol = {}
        self.symbol_token = {}
        self.exchange = None
        
        # Initialize Zerodha connection
        self._initialize_zerodha()
        self._create_required_tables()
        
        logger.info("Stock Strategy Bot initialized successfully")

    def _get_db_config(self) -> Dict[str, str]:
        """Get database configuration from config file or use defaults."""
        config = configparser.ConfigParser()
        if os.path.exists('db_config.ini'):
            config.read('db_config.ini')
            return config['mysql']
        else:
            return {
                'host': 'localhost',
                'port': '3306',
                'user': 'root',
                'password': 'vinayak123',
                'database': 'option_chain_data'
            }

    def _connect_to_db(self) -> None:
        """Connect to the MySQL database."""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=int(self.db_config['port']),
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info("Connected to database successfully")
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            sys.exit(1)

    def _initialize_zerodha(self):
        """Initialize Zerodha API connection"""
        try:
            self._get_login_credentials()
            self._get_access_token()
            self._get_kite_object()
            self._start_websocket()
            self._get_exchange_data()
            logger.info("Zerodha API initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Zerodha API: {e}")
            sys.exit(1)

    def _get_login_credentials(self):
        """Get Zerodha login credentials"""
        def login_credentials():
            print("---- Enter your Zerodha Login Credentials  ----")
            self.login_credential = {
                "api_key": str(input("Enter API Key : ").strip()),
                "api_secret": str(input("Enter API Secret : ")).strip()
            }
            if input("Press Y to save login credential and any key to bypass : ").strip().upper() == "Y":
                with open("zerodha_login_details.json", "w") as f:
                    json.dump(self.login_credential, f)
                print("Data Saved...")

        while True:
            try:
                with open("zerodha_login_details.json", "r") as f:
                    self.login_credential = json.load(f)
                break
            except:
                login_credentials()

    def _get_access_token(self):
        """Get access token for Zerodha API"""
        def login():
            print("Trying Log In...")
            if self.login_credential["api_key"] == "TradeViaPython":
                print("Login url : https://kite.zerodha.com")
                self.access_token = input("Login and enter your 'enctoken' here : ")
            else:
                kite = KiteConnect(api_key=self.login_credential["api_key"])
                print("Login url : ", kite.login_url())
                request_tkn = input("Login and enter your 'request_token' here : ")
                try:
                    self.access_token = kite.generate_session(
                        request_token=request_tkn,
                        api_secret=self.login_credential["api_secret"]
                    )['access_token']
                except Exception as e:
                    print(f"Login Failed {e}!!!!!")
                    
            os.makedirs("AccessToken", exist_ok=True)
            with open(f"AccessToken/{datetime.datetime.now().date()}.json", "w") as f:
                json.dump(self.access_token, f)

        while True:
            if os.path.exists(f"AccessToken/{datetime.datetime.now().date()}.json"):
                with open(f"AccessToken/{datetime.datetime.now().date()}.json", "r") as f:
                    self.access_token = json.load(f)
                break
            else:
                login()

    def _get_kite_object(self):
        """Get Kite Connect object"""
        try:
            if self.login_credential["api_key"] == "TradeViaPython":
                # Custom KiteApp class for enctoken
                class KiteApp:
                    def __init__(self, enctoken):
                        self.enctoken = enctoken
                        self.headers = {"Authorization": f"enctoken {self.enctoken}"}
                        self.session = requests.session()
                        self.root_url = "https://kite.zerodha.com/oms"
                        self.session.get(self.root_url, headers=self.headers)

                    def instruments(self, exchange=None):
                        data = self.session.get("https://api.kite.trade/instruments").text.split("\n")
                        Exchange = []
                        for i in data[1:-1]:
                            row = i.split(",")
                            if exchange is None or exchange == row[11]:
                                Exchange.append({
                                    'instrument_token': int(row[0]),
                                    'exchange_token': row[1],
                                    'tradingsymbol': row[2],
                                    'name': row[3][1:-1],
                                    'last_price': float(row[4]),
                                    'expiry': dateutil.parser.parse(row[5]).date() if row[5] != "" else None,
                                    'strike': float(row[6]),
                                    'tick_size': float(row[7]),
                                    'lot_size': int(row[8]),
                                    'instrument_type': row[9],
                                    'segment': row[10],
                                    'exchange': row[11]
                                })
                        return Exchange

                    def profile(self):
                        profile = self.session.get(f"{self.root_url}/user/profile", headers=self.headers).json()["data"]
                        return profile

                    def positions(self):
                        positions = self.session.get(f"{self.root_url}/portfolio/positions", headers=self.headers).json()["data"]
                        return positions

                    def place_order(self, variety, exchange, tradingsymbol, transaction_type, quantity, product, order_type,
                                  price=None, validity=None, disclosed_quantity=None, trigger_price=None, squareoff=None,
                                  stoploss=None, trailing_stoploss=None, tag=None):
                        params = locals()
                        del params["self"]
                        for k in list(params.keys()):
                            if params[k] is None:
                                del params[k]
                        order_id = self.session.post(f"{self.root_url}/orders/{variety}",
                                                   data=params, headers=self.headers).json()["data"]["order_id"]
                        return order_id

                self.kite = KiteApp(enctoken=self.access_token)
            else:
                self.kite = KiteConnect(api_key=self.login_credential["api_key"], access_token=self.access_token)

            self.user_id = self.kite.profile()["user_id"]
            user_name = self.kite.profile()["user_name"]
            logger.info(f"Logged In: {self.user_id} as {user_name}")
        except Exception as e:
            logger.error(f"Login Error: {e}")
            sys.exit(1)

    def _start_websocket(self):
        """Start WebSocket connection for live data"""
        access_token = (self.access_token + "&user_id=" + self.user_id 
                       if self.login_credential["api_key"] == "TradeViaPython" 
                       else self.access_token)
        
        self.kws = KiteTicker(api_key=self.login_credential["api_key"], access_token=access_token)
        self.tick_data = {}
        self.token_symbol = {}

        def on_ticks(ws, ticks):
            for tick in ticks:
                symbol = self.token_symbol.get(tick["instrument_token"])
                if symbol:
                    self.tick_data[symbol] = tick

        self.kws.on_ticks = on_ticks
        self.kws.connect(threaded=True)
        
        while not self.kws.is_connected():
            time.sleep(1)
        logger.info("WebSocket Connected")

    def _get_exchange_data(self):
        """Download exchange data"""
        while True:
            try:
                logger.info("Downloading exchange data...")
                self.exchange = pd.DataFrame(self.kite.instruments())
                break
            except Exception as e:
                logger.error(f"Exchange Download Error: {e}")
                time.sleep(10)

    def _create_required_tables(self):
        """Create required database tables if they don't exist"""
        try:
            # Create Live_filtered_stocks table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS Live_filtered_stocks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50),
                    ltp DECIMAL(10,2),
                    max_put_oi_strike DECIMAL(10,2),
                    max_call_oi_strike DECIMAL(10,2),
                    support DECIMAL(10,2),
                    resistance DECIMAL(10,2),
                    timestamp DATETIME,
                    UNIQUE KEY unique_symbol (symbol)
                ) ENGINE=InnoDB
            """)

            # Create live_stock_trades table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS live_stock_trades (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50),
                    buy_price DECIMAL(10,2),
                    quantity INT,
                    investment_amount DECIMAL(15,2),
                    support_level DECIMAL(10,2),
                    resistance_level DECIMAL(10,2),
                    stop_loss DECIMAL(10,2),
                    target_price DECIMAL(10,2),
                    buy_time DATETIME,
                    status ENUM('OPEN', 'CLOSED') DEFAULT 'OPEN',
                    close_price DECIMAL(10,2) NULL,
                    close_time DATETIME NULL,
                    pnl DECIMAL(15,2) NULL,
                    close_reason VARCHAR(100) NULL,
                    order_id VARCHAR(50) NULL
                ) ENGINE=InnoDB
            """)

            # Create ORDER_LOG table if it doesn't exist
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS ORDER_LOG (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME,
                    symbol VARCHAR(50),
                    action VARCHAR(20),
                    quantity INT,
                    price DECIMAL(10,2),
                    amount DECIMAL(15,2),
                    reason VARCHAR(500),
                    order_id VARCHAR(50),
                    status VARCHAR(20)
                ) ENGINE=InnoDB
            """)

            self.conn.commit()
            logger.info("Required tables created/verified successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")

    def _filter_stocks(self) -> List[Dict]:
        """Filter stocks based on the specified criteria"""
        try:
            logger.info("Filtering stocks based on criteria...")
            
            # First check if option_data_need_live_stocks table exists
            self.cursor.execute("SHOW TABLES LIKE 'option_data_need_live_stocks'")
            table_exists = self.cursor.fetchone()
            
            if not table_exists:
                logger.warning("Table option_data_need_live_stocks not found, trying alternative table names")
                # Try other possible table names
                for table_name in ['option_data_need_live', 'option_chain_data', 'live_option_data']:
                    self.cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if self.cursor.fetchone():
                        logger.info(f"Using table: {table_name}")
                        table_name = table_name
                        break
                else:
                    logger.error("No suitable option data table found")
                    return []
            else:
                table_name = 'option_data_need_live_stocks'
            
            # Query to get all stocks data
            query = f"""
                SELECT symbol, Spot_LTP, Max_Put_OI_Strike, Max_Call_OI_Strike, Max_Put_OI
                FROM {table_name}
                WHERE Spot_LTP > 100 AND Max_Put_OI_Strike IS NOT NULL AND Max_Call_OI_Strike IS NOT NULL
            """
            
            self.cursor.execute(query)
            stocks_data = self.cursor.fetchall()
            
            if not stocks_data:
                logger.warning("No stocks data found in the database")
                return []
            
            filtered_stocks = []
            
            for stock in stocks_data:
                try:
                    symbol = stock['symbol']
                    ltp = float(stock['Spot_LTP']) if stock['Spot_LTP'] else 0
                    max_put_oi_strike = float(stock['Max_Put_OI_Strike']) if stock['Max_Put_OI_Strike'] else 0
                    max_call_oi_strike = float(stock['Max_Call_OI_Strike']) if stock['Max_Call_OI_Strike'] else 0
                    
                    # Skip if any value is 0 or None
                    if ltp <= 0 or max_put_oi_strike <= 0 or max_call_oi_strike <= 0:
                        continue
                    
                    # Check if LTP is within 5% range of max_put_oi_strike
                    ltp_range_lower = max_put_oi_strike * 0.95
                    ltp_range_upper = max_put_oi_strike * 1.05
                    
                    if ltp_range_lower <= ltp <= ltp_range_upper:
                        # Check if 7% of Max_Put_OI_Strike + Max_Put_OI_Strike >= Max_Call_OI_Strike
                        threshold = (0.07 * max_put_oi_strike) + max_put_oi_strike
                        
                        if threshold <= max_call_oi_strike:
                            support = max_put_oi_strike
                            resistance = max_call_oi_strike
                            
                            filtered_stocks.append({
                                'symbol': symbol,
                                'ltp': ltp,
                                'max_put_oi_strike': max_put_oi_strike,
                                'max_call_oi_strike': max_call_oi_strike,
                                'support': support,
                                'resistance': resistance
                            })
                            
                except Exception as e:
                    logger.error(f"Error processing stock {stock.get('symbol', 'Unknown')}: {e}")
                    continue
            
            logger.info(f"Filtered {len(filtered_stocks)} stocks out of {len(stocks_data)} total stocks")
            
            # Send telegram notification if stocks are filtered
            if filtered_stocks:
                message = f"Stock Strategy: Found {len(filtered_stocks)} filtered stocks: {', '.join([s['symbol'] for s in filtered_stocks])}"
                send_telegram_message(message)
            
            return filtered_stocks
            
        except Exception as e:
            logger.error(f"Error filtering stocks: {e}")
            return []

    def _update_filtered_stocks_list(self):
        """Update the filtered stocks list every 10 minutes"""
        current_time = time.time()
        
        if current_time - self.last_filter_list_update >= FILTER_LIST_UPDATE_INTERVAL:
            new_filtered_stocks = self._filter_stocks()
            
            # Compare with previous list
            if new_filtered_stocks != self.filtered_stocks:
                old_symbols = set([s['symbol'] for s in self.filtered_stocks])
                new_symbols = set([s['symbol'] for s in new_filtered_stocks])
                
                added_symbols = new_symbols - old_symbols
                removed_symbols = old_symbols - new_symbols
                
                if added_symbols or removed_symbols:
                    message = f"Stock Strategy Update:\n"
                    if added_symbols:
                        message += f"Added: {', '.join(added_symbols)}\n"
                    if removed_symbols:
                        message += f"Removed: {', '.join(removed_symbols)}"
                    send_telegram_message(message)
            
            self.filtered_stocks = new_filtered_stocks
            self.last_filter_list_update = current_time
            logger.info(f"Updated filtered stocks list: {len(self.filtered_stocks)} stocks")

    def _subscribe_to_stocks(self):
        """Subscribe to filtered stocks for live data"""
        if not self.filtered_stocks:
            logger.warning("No filtered stocks to subscribe to")
            return
            
        try:
            # Clear existing subscriptions
            if self.token_symbol:
                try:
                    self.kws.unsubscribe(list(self.token_symbol.keys()))
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"Error unsubscribing: {e}")
                    
            self.tick_data = {}
            self.token_symbol = {}
            self.symbol_token = {}
            
            # Get tokens for filtered stocks
            tokens_to_subscribe = []
            for stock in self.filtered_stocks:
                symbol = stock['symbol']
                
                # Find stock in exchange data - try multiple formats
                stock_df = self.exchange[
                    (self.exchange['exchange'] == 'NSE') & 
                    (self.exchange['tradingsymbol'] == symbol)
                ]
                
                # If not found, try with -EQ suffix
                if stock_df.empty:
                    stock_df = self.exchange[
                        (self.exchange['exchange'] == 'NSE') & 
                        (self.exchange['tradingsymbol'] == symbol + '-EQ')
                    ]
                
                if not stock_df.empty:
                    token = int(stock_df.iloc[0]['instrument_token'])
                    trading_symbol = f"NSE:{symbol}"
                    
                    self.token_symbol[token] = trading_symbol
                    self.symbol_token[trading_symbol] = token
                    tokens_to_subscribe.append(token)
                    
                    logger.info(f"Found token {token} for symbol {symbol}")
                else:
                    logger.warning(f"Could not find token for symbol: {symbol}")
            
            if tokens_to_subscribe:
                self.kws.subscribe(tokens_to_subscribe)
                self.kws.set_mode(self.kws.MODE_FULL, tokens_to_subscribe)
                logger.info(f"Subscribed to {len(tokens_to_subscribe)} stocks for live data")
            else:
                logger.warning("No tokens found to subscribe to")
                
        except Exception as e:
            logger.error(f"Error subscribing to stocks: {e}")

    def _update_live_filtered_stocks(self):
        """Update live filtered stocks table with current LTP data"""
        try:
            for stock in self.filtered_stocks:
                symbol = stock['symbol']
                trading_symbol = f"NSE:{symbol}"
                
                if trading_symbol in self.tick_data:
                    current_ltp = self.tick_data[trading_symbol]['last_price']
                    
                    # Update price history for crossover detection
                    if symbol not in self.price_history:
                        self.price_history[symbol] = []
                    
                    self.price_history[symbol].append({
                        'ltp': current_ltp,
                        'timestamp': datetime.datetime.now()
                    })
                    
                    # Keep only last 10 price points
                    if len(self.price_history[symbol]) > 10:
                        self.price_history[symbol] = self.price_history[symbol][-10:]
                    
                    # Calculate support and resistance with buffers
                    support = stock['support']
                    resistance = stock['resistance']
                    
                    # Update or insert into Live_filtered_stocks table
                    self.cursor.execute("""
                        INSERT INTO Live_filtered_stocks (
                            symbol, ltp, max_put_oi_strike, max_call_oi_strike, 
                            support, resistance, timestamp
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        ltp = VALUES(ltp),
                        max_put_oi_strike = VALUES(max_put_oi_strike),
                        max_call_oi_strike = VALUES(max_call_oi_strike),
                        support = VALUES(support),
                        resistance = VALUES(resistance),
                        timestamp = VALUES(timestamp)
                    """, (
                        symbol, current_ltp, stock['max_put_oi_strike'],
                        stock['max_call_oi_strike'], support, resistance,
                        datetime.datetime.now()
                    ))
            
            self.conn.commit()
            
        except Exception as e:
            logger.error(f"Error updating live filtered stocks: {e}")

    def _detect_crossover(self, symbol: str, current_ltp: float, level: float, direction: str) -> bool:
        """
        Detect if price has crossed a level in the specified direction
        direction: 'above' for crossing from below to above, 'below' for crossing from above to below
        """
        if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
            return False
        
        # Get previous price
        prev_ltp = self.price_history[symbol][-2]['ltp'] if len(self.price_history[symbol]) >= 2 else current_ltp
        
        if direction == 'above':
            # Check if price crossed from below to above
            return prev_ltp <= level and current_ltp > level
        elif direction == 'below':
            # Check if price crossed from above to below
            return prev_ltp >= level and current_ltp < level
        
        return False

    def _check_buy_signals(self):
        """Check for buy signals based on support crossover"""
        try:
            for stock in self.filtered_stocks:
                symbol = stock['symbol']
                trading_symbol = f"NSE:{symbol}"
                
                if trading_symbol not in self.tick_data:
                    continue
                    
                current_ltp = self.tick_data[trading_symbol]['last_price']
                support = stock['support']
                resistance = stock['resistance']
                
                # Calculate support with buffer
                support_with_buffer = support * (1 + BUFFER_PERCENTAGE / 100)
                
                # Check if we already have an open position for this stock
                self.cursor.execute("""
                    SELECT COUNT(*) as count FROM live_stock_trades 
                    WHERE symbol = %s AND status = 'OPEN'
                """, (symbol,))
                
                existing_position = self.cursor.fetchone()['count']
                
                if existing_position > 0:
                    continue  # Skip if we already have a position
                
                # Check for crossover from below to above support+buffer
                if self._detect_crossover(symbol, current_ltp, support_with_buffer, 'above'):
                    logger.info(f"BUY SIGNAL DETECTED for {symbol}: LTP {current_ltp} crossed above support+buffer {support_with_buffer}")
                    self._execute_buy_order(symbol, current_ltp, support, resistance)
                        
        except Exception as e:
            logger.error(f"Error checking buy signals: {e}")

    def _execute_buy_order(self, symbol: str, current_ltp: float, support: float, resistance: float):
        """Execute buy order for a stock"""
        try:
            # Calculate quantity based on investment amount
            quantity = int(INVESTMENT_AMOUNT / current_ltp)
            
            if quantity <= 0:
                logger.warning(f"Cannot buy {symbol}: calculated quantity is 0")
                return
                
            actual_investment = quantity * current_ltp
            
            # Calculate stop loss and target
            stop_loss = current_ltp * (1 - SL_PERCENTAGE / 100)
            target_price = resistance * (1 - TARGET_BUFFER / 100)
            
            # Place order through Zerodha (uncomment for live trading)
            try:
                # order_id = self.kite.place_order(
                #     variety="regular",
                #     exchange="NSE",
                #     tradingsymbol=symbol,
                #     transaction_type="BUY",
                #     quantity=quantity,
                #     product="MIS",  # Intraday
                #     order_type="MARKET"
                # )
                logger.info(f"Live order placed with ID: {order_id}")
            except Exception as e:
                logger.error(f"Error placing live order: {e}")
                order_id = f"TEST_ORDER_{int(time.time())}"  # Fallback to test order ID
            
            # Insert into live_stock_trades table
            self.cursor.execute("""
                INSERT INTO live_stock_trades (
                    symbol, buy_price, quantity, investment_amount,
                    support_level, resistance_level, stop_loss, target_price,
                    buy_time, status, order_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, 'OPEN', %s)
            """, (
                symbol, current_ltp, quantity, actual_investment,
                support, resistance, stop_loss, target_price,
                datetime.datetime.now(), order_id
            ))
            
            # Log the order
            self._log_order(
                symbol=symbol,
                action='BUY',
                quantity=quantity,
                price=current_ltp,
                amount=actual_investment,
                reason=f'Support crossover signal at {current_ltp}, Support: {support}, Buffer: {support * (1 + BUFFER_PERCENTAGE / 100)}',
                order_id=order_id,
                status='EXECUTED'
            )
            
            self.conn.commit()
            
            # Send telegram notification
            message = f"🟢 BUY ORDER EXECUTED\nSymbol: {symbol}\nPrice: {current_ltp}\nQty: {quantity}\nInvestment: ₹{actual_investment:,.2f}\nSL: {stop_loss:.2f}\nTarget: {target_price:.2f}"
            send_telegram_message(message)
            
            logger.info(f"BUY ORDER: {symbol} - Qty: {quantity}, Price: {current_ltp}, "
                       f"Investment: {actual_investment}, SL: {stop_loss}, Target: {target_price}")
                       
        except Exception as e:
            logger.error(f"Error executing buy order for {symbol}: {e}")
            
            # Log the failed order
            self._log_order(
                symbol=symbol,
                action='BUY',
                quantity=quantity if 'quantity' in locals() else 0,
                price=current_ltp,
                amount=0,
                reason=f'Buy order failed: {str(e)}',
                order_id='',
                status='FAILED'
            )

    def _check_exit_signals(self):
        """Check for exit signals for open positions"""
        try:
            # Get all open positions
            self.cursor.execute("""
                SELECT * FROM live_stock_trades WHERE status = 'OPEN'
            """)
            
            open_positions = self.cursor.fetchall()
            
            for position in open_positions:
                symbol = position['symbol']
                trading_symbol = f"NSE:{symbol}"
                
                if trading_symbol not in self.tick_data:
                    continue
                    
                current_ltp = self.tick_data[trading_symbol]['last_price']
                buy_price = float(position['buy_price'])
                stop_loss = float(position['stop_loss'])
                target_price = float(position['target_price'])
                resistance_level = float(position['resistance_level'])
                quantity = position['quantity']
                
                should_sell = False
                sell_reason = ""
                
                # Check for stop loss
                if current_ltp <= stop_loss:
                    should_sell = True
                    sell_reason = f"Stop loss hit at {current_ltp} (SL: {stop_loss})"
                
                # Check for target (resistance - 1% buffer)
                elif current_ltp >= target_price:
                    should_sell = True
                    sell_reason = f"Target reached at {current_ltp} (Target: {target_price})"
                
                # Additional check: if price crosses below resistance (alternative exit)
                elif self._detect_crossover(symbol, current_ltp, resistance_level, 'below'):
                    should_sell = True
                    sell_reason = f"Price crossed below resistance {resistance_level} at {current_ltp}"
                
                if should_sell:
                    logger.info(f"EXIT SIGNAL DETECTED for {symbol}: {sell_reason}")
                    self._execute_sell_order(position, current_ltp, sell_reason)
                    
        except Exception as e:
            logger.error(f"Error checking exit signals: {e}")

    def _execute_sell_order(self, position: Dict, current_ltp: float, reason: str):
        """Execute sell order for a position"""
        try:
            symbol = position['symbol']
            quantity = position['quantity']
            buy_price = float(position['buy_price'])
            position_id = position['id']
            
            # Calculate P&L
            pnl = (current_ltp - buy_price) * quantity
            
            # Place sell order through Zerodha (uncomment for live trading)
            try:
                # order_id = self.kite.place_order(
                #     variety="regular",
                #     exchange="NSE",
                #     tradingsymbol=symbol,
                #     transaction_type="SELL",
                #     quantity=quantity,
                #     product="MIS",
                #     order_type="MARKET"
                # )
                logger.info(f"Live sell order placed with ID: {order_id}")
            except Exception as e:
                logger.error(f"Error placing live sell order: {e}")
                order_id = f"TEST_SELL_{int(time.time())}"  # Fallback to test order ID
            
            # Update position in database
            self.cursor.execute("""
                UPDATE live_stock_trades 
                SET status = 'CLOSED', close_price = %s, close_time = %s, 
                    pnl = %s, close_reason = %s
                WHERE id = %s
            """, (
                current_ltp, datetime.datetime.now(), pnl, reason, position_id
            ))
            
            # Log the order
            self._log_order(
                symbol=symbol,
                action='SELL',
                quantity=quantity,
                price=current_ltp,
                amount=current_ltp * quantity,
                reason=reason,
                order_id=order_id,
                status='EXECUTED'
            )
            
            self.conn.commit()
            
            # Send telegram notification
            pnl_emoji = "🟢" if pnl >= 0 else "🔴"
            message = f"{pnl_emoji} SELL ORDER EXECUTED\nSymbol: {symbol}\nBuy: {buy_price:.2f}\nSell: {current_ltp:.2f}\nQty: {quantity}\nP&L: ₹{pnl:,.2f}\nReason: {reason}"
            send_telegram_message(message)
            
            logger.info(f"SELL ORDER: {symbol} - Qty: {quantity}, Price: {current_ltp}, "
                       f"P&L: {pnl:.2f}, Reason: {reason}")
                       
        except Exception as e:
            logger.error(f"Error executing sell order for {position['symbol']}: {e}")

    def _log_order(self, symbol: str, action: str, quantity: int, price: float, 
                   amount: float, reason: str, order_id: str, status: str):
        """Log order details to ORDER_LOG table"""
        try:
            self.cursor.execute("""
                INSERT INTO ORDER_LOG (
                    timestamp, symbol, action, quantity, price, amount, reason, order_id, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                datetime.datetime.now(), symbol, action, quantity, price, 
                amount, reason, order_id, status
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error logging order: {e}")

    def _check_month_end_squareoff(self):
        """Check and execute month-end square-off for all positions"""
        try:
            current_date = datetime.datetime.now()
            
            # Check if it's the last day of the month
            next_month = current_date.replace(day=28) + datetime.timedelta(days=4)
            last_day_of_month = next_month - datetime.timedelta(days=next_month.day)
            
            if current_date.date() == last_day_of_month.date():
                # Square off all open positions
                self.cursor.execute("""
                    SELECT * FROM live_stock_trades WHERE status = 'OPEN'
                """)
                
                open_positions = self.cursor.fetchall()
                
                for position in open_positions:
                    symbol = position['symbol']
                    trading_symbol = f"NSE:{symbol}"
                    
                    if trading_symbol in self.tick_data:
                        current_ltp = self.tick_data[trading_symbol]['last_price']
                        self._execute_sell_order(position, current_ltp, "Month-end square-off")
                        
                logger.info(f"Month-end square-off completed for {len(open_positions)} positions")
                
        except Exception as e:
            logger.error(f"Error in month-end square-off: {e}")

    def _is_market_open(self) -> bool:
        """Check if the market is currently open"""
        now = datetime.datetime.now().time()
        market_start = datetime.time(9, 15)
        market_end = datetime.time(15, 30)
        return market_start <= now <= market_end

    def run(self):
        """Main execution loop"""
        logger.info("Starting Stock Strategy Bot...")
        
        # Send startup notification
        send_telegram_message("🚀 Stock Strategy Bot Started!")
        
        try:
            while True:
                if not self._is_market_open():
                    current_time = datetime.datetime.now().strftime("%H:%M:%S")
                    logger.info(f"Market is closed at {current_time}. Waiting for market hours (9:15 AM - 3:30 PM)...")
                    time.sleep(60)  # Check every minute when market is closed
                    continue
                
                start_time = time.time()
                
                try:
                    # Update filtered stocks list every 10 minutes
                    self._update_filtered_stocks_list()
                    
                    # Subscribe to filtered stocks for live data every 20 minutes
                    if self.filtered_stocks:
                        current_time = time.time()
                        if current_time - self.last_filter_time >= FILTER_UPDATE_INTERVAL:
                            logger.info("Updating stock subscriptions...")
                            self._subscribe_to_stocks()
                            self.last_filter_time = current_time
                    
                    # Update live filtered stocks table with current LTP
                    if self.tick_data:
                        self._update_live_filtered_stocks()
                        
                        # Check for buy signals
                        self._check_buy_signals()
                        
                        # Check for exit signals on open positions
                        self._check_exit_signals()
                    else:
                        logger.debug("No tick data available yet")
                    
                    # Check for month-end square-off
                    self._check_month_end_squareoff()
                    
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                
                # Ensure we run every second during market hours
                elapsed_time = time.time() - start_time
                sleep_time = max(0, 1.0 - elapsed_time)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
        except KeyboardInterrupt:
            logger.info("Stock Strategy Bot stopped by user")
            send_telegram_message("⏹️ Stock Strategy Bot stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in Stock Strategy Bot: {e}")
            send_telegram_message(f"❌ Stock Strategy Bot crashed: {str(e)}")
        finally:
            if self.kws and self.kws.is_connected():
                self.kws.close()
            if self.conn:
                self.conn.close()
            logger.info("Stock Strategy Bot shutdown completed")

def main():
    """Main function to start the Stock Strategy Bot"""
    try:
        strategy_bot = StockStrategy()
        strategy_bot.run()
    except Exception as e:
        logger.error(f"Failed to start Stock Strategy Bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()