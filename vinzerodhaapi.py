import os, json
import copy
import time
import dateutil.parser
import threading
import sys
import requests
import urllib
import datetime
import configparser
import pymysql
import logging
from urllib.parse import quote_plus
import pyotp
enctoken="om99coYsxRsgdBrmf8adNkVXv9V33L9K38qPy1LJ6YWWgkQZvlPvYMzm5Oi7IZ8HMseApX33tIM+xBFCCY4EfDumB4+y0f6VHe4gaEAADbmVU++0I9tpXQ=="
global kite, login_credential, access_token, user_id
class KiteApp:
    # Products
    PRODUCT_MIS = "MIS"
    PRODUCT_CNC = "CNC"
    PRODUCT_NRML = "NRML"
    PRODUCT_CO = "CO"

    # Order types
    ORDER_TYPE_MARKET = "MARKET"
    ORDER_TYPE_LIMIT = "LIMIT"
    ORDER_TYPE_SLM = "SL-M"
    ORDER_TYPE_SL = "SL"

    # Varities
    VARIETY_REGULAR = "regular"
    VARIETY_CO = "co"
    VARIETY_AMO = "amo"

    # Transaction type
    TRANSACTION_TYPE_BUY = "BUY"
    TRANSACTION_TYPE_SELL = "SELL"

    # Validity
    VALIDITY_DAY = "DAY"
    VALIDITY_IOC = "IOC"

    # Exchanges
    EXCHANGE_NSE = "NSE"
    EXCHANGE_BSE = "BSE"
    EXCHANGE_NFO = "NFO"
    EXCHANGE_CDS = "CDS"
    EXCHANGE_BFO = "BFO"
    EXCHANGE_MCX = "MCX"

    def __init__(self, enctoken):
        self.enctoken = enctoken
        self.headers = {"Authorization": f"enctoken {self.enctoken}"}
        self.session = requests.session()
        self.root_url = "https://kite.zerodha.com/oms"
        self.session.get(self.root_url, headers=self.headers)

    def instruments(self, exchange=None):
        data = self.session.get(f"https://api.kite.trade/instruments").text.split("\n")
        Exchange = []
        for i in data[1:-1]:
            row = i.split(",")
            if exchange is None or exchange == row[11]:
                Exchange.append(
                    {'instrument_token': int(row[0]), 'exchange_token': row[1], 'tradingsymbol': row[2],
                        'name': row[3][1:-1], 'last_price': float(row[4]),
                        'expiry': dateutil.parser.parse(row[5]).date() if row[5] != "" else None,
                        'strike': float(row[6]), 'tick_size': float(row[7]), 'lot_size': int(row[8]),
                        'instrument_type': row[9], 'segment': row[10],
                        'exchange': row[11]})
        return Exchange

    def historical_data(self, instrument_token, from_date, to_date, interval, continuous=False, oi=False):
        params = {"from": from_date,
                    "to": to_date,
                    "interval": interval,
                    "continuous": 1 if continuous else 0,
                    "oi": 1 if oi else 0}
        lst = self.session.get(
            f"{self.root_url}/instruments/historical/{instrument_token}/{interval}", params=params,
            headers=self.headers).json()["data"]["candles"]
        records = []
        for i in lst:
            record = {"date": dateutil.parser.parse(i[0]), "open": i[1], "high": i[2], "low": i[3],
                        "close": i[4], "volume": i[5], }
            if len(i) == 7:
                record["oi"] = i[6]
            records.append(record)
        return records

    def margins(self):
        margins = self.session.get(f"{self.root_url}/user/margins", headers=self.headers).json()["data"]
        return margins

    def profile(self):
        profile = self.session.get(f"{self.root_url}/user/profile", headers=self.headers).json()["data"]
        return profile

    def orders(self):
        orders = self.session.get(f"{self.root_url}/orders", headers=self.headers).json()["data"]
        return orders

    def positions(self):
        positions = self.session.get(f"{self.root_url}/portfolio/positions", headers=self.headers).json()[
            "data"]
        return positions

    def place_order(self, variety, exchange, tradingsymbol, transaction_type, quantity, product, order_type,
                    price=None,
                    validity=None, disclosed_quantity=None, trigger_price=None, squareoff=None,
                    stoploss=None,
                    trailing_stoploss=None, tag=None):
        params = locals()
        del params["self"]
        for k in list(params.keys()):
            if params[k] is None:
                del params[k]
        order_id = self.session.post(f"{self.root_url}/orders/{variety}",
                                        data=params, headers=self.headers).json()
        return order_id

    def modify_order(self, variety, order_id, parent_order_id=None, quantity=None, price=None,
                        order_type=None,
                        trigger_price=None, validity=None, disclosed_quantity=None):
        params = locals()
        del params["self"]
        for k in list(params.keys()):
            if params[k] is None:
                del params[k]

        order_id = self.session.put(f"{self.root_url}/orders/{variety}/{order_id}",
                                    data=params, headers=self.headers).json()["data"][
            "order_id"]
        return order_id

    def cancel_order(self, variety, order_id, parent_order_id=None):
        order_id = self.session.delete(f"{self.root_url}/orders/{variety}/{order_id}",
                                        data={"parent_order_id": parent_order_id} if parent_order_id else {},
                                        headers=self.headers).json()["data"]["order_id"]
        return order_id
# Initialize Kite with the access token
kite = KiteApp(enctoken=enctoken)

# Example of how to use the API (commented out to avoid accidental execution)
"""
try:
    order_id = kite.place_order(tradingsymbol="BANKNIFTY25MAY55100CE",
                                exchange=kite.EXCHANGE_NFO,
                                transaction_type=kite.TRANSACTION_TYPE_BUY,
                                quantity=30,
                                variety=kite.VARIETY_AMO,
                                order_type=kite.ORDER_TYPE_LIMIT,
                                price=250,
                                product=kite.PRODUCT_NRML,
                                validity=kite.VALIDITY_DAY)

    logging.info("Order placed. ID is: {}".format(order_id))
except Exception as e:
    logging.error("Order placement failed: {}".format(str(e)))

# Fetch all orders
orders = kite.orders()
logging.info(f"Found {len(orders)} orders")

# Get all current positions
positions = kite.positions()
logging.info(f"Found {len(positions.get('day', []))} day positions")
"""

# Log successful initialization
# logging.info("Zerodha API initialized successfully")

# Telegram Configuration
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "-4735843712"
TELEGRAM_BASE_URL = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"

# Market Hours Configuration
MARKET_START_TIME = datetime.time(9, 15)  # 9:15 AM
MARKET_END_TIME = datetime.time(15, 30)   # 3:30 PM
MARKET_DAYS = [0, 1, 2, 3, 4]  # Monday to Friday (0-6, Monday is 0)

# Global variable to track processed orders
processed_orders = set()
order_status_tracker = {}  # Dictionary to track order status changes

def send_telegram_message(message):
    """Send a message to Telegram channel"""
    try:
        encoded_message = quote_plus(message)
        url = f"{TELEGRAM_BASE_URL}?chat_id={TELEGRAM_CHAT_ID}&text={encoded_message}"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            logging.info("Telegram message sent successfully")
        else:
            logging.error(f"Failed to send Telegram message: {response.status_code}")
    except Exception as e:
        logging.error(f"Error sending Telegram message: {str(e)}")

def format_order_alert(order):
    """Format order data into a readable alert message"""
    try:
        symbol = order.get('tradingsymbol', 'N/A')
        qty = order.get('quantity', 0)
        filled_qty = order.get('filled_quantity', 0)
        price = order.get('average_price', order.get('price', 0))
        transaction_type = order.get('transaction_type', 'N/A')
        status = order.get('status', 'N/A')
        order_type = order.get('order_type', 'N/A')
        exchange = order.get('exchange', 'N/A')
        order_id = order.get('order_id', 'N/A')
        timestamp = order.get('order_timestamp', 'N/A')
        product = order.get('product', 'N/A')
        
        # Format the message
        message = f"""🚨 NEW ORDER ALERT 🚨

📊 Symbol: {symbol}
📈 Type: {transaction_type}
📋 Status: {status}
💰 Qty: {qty} | Filled: {filled_qty}
💵 Price: ₹{price:.2f}
🏪 Exchange: {exchange}
📄 Order Type: {order_type}
🛡️ Product: {product}
🆔 Order ID: {order_id}
⏰ Time: {timestamp}

━━━━━━━━━━━━━━━━━━"""
        
        return message
    except Exception as e:
        logging.error(f"Error formatting order alert: {str(e)}")
        return f"Error formatting order: {str(e)}"

def format_status_change_alert(order, old_status, new_status):
    """Format order status change alert message"""
    try:
        symbol = order.get('tradingsymbol', 'N/A')
        qty = order.get('quantity', 0)
        filled_qty = order.get('filled_quantity', 0)
        pending_qty = order.get('pending_quantity', 0)
        price = order.get('average_price', order.get('price', 0))
        transaction_type = order.get('transaction_type', 'N/A')
        order_type = order.get('order_type', 'N/A')
        exchange = order.get('exchange', 'N/A')
        order_id = order.get('order_id', 'N/A')
        timestamp = order.get('exchange_update_timestamp', order.get('order_timestamp', 'N/A'))
        product = order.get('product', 'N/A')
        status_message = order.get('status_message', '')
        
        # Choose appropriate emoji based on status
        status_emoji = {
            'COMPLETE': '✅',
            'CANCELLED': '❌',
            'REJECTED': '🚫',
            'OPEN': '🔄',
            'TRIGGER PENDING': '⏳',
            'CANCELLED AMO': '❌',
            'MODIFY PENDING': '🔄',
            'CANCEL PENDING': '⏳'
        }.get(new_status, '🔔')
        
        # Format the message
        message = f"""{status_emoji} ORDER STATUS UPDATE {status_emoji}

📊 Symbol: {symbol}
📈 Type: {transaction_type}
🔄 Status: {old_status} → {new_status}
💰 Qty: {qty} | Filled: {filled_qty} | Pending: {pending_qty}
💵 Price: ₹{price:.2f}
🏪 Exchange: {exchange}
📄 Order Type: {order_type}
🛡️ Product: {product}
🆔 Order ID: {order_id}
⏰ Updated: {timestamp}"""

        if status_message:
            message += f"\n💬 Message: {status_message}"
            
        message += "\n\n━━━━━━━━━━━━━━━━━━"
        
        return message
    except Exception as e:
        logging.error(f"Error formatting status change alert: {str(e)}")
        return f"Error formatting status change: {str(e)}"

def is_market_hours():
    """Check if current time is within market hours"""
    now = datetime.datetime.now()
    current_time = now.time()
    current_day = now.weekday()
    
    # Check if it's a trading day (Monday to Friday)
    if current_day not in MARKET_DAYS:
        return False
    
    # Check if current time is within market hours
    if MARKET_START_TIME <= current_time <= MARKET_END_TIME:
        return True
    
    return False

def is_holiday():
    """Check if today is a market holiday (basic implementation)"""
    # You can expand this function to include actual holiday dates
    # For now, it only checks weekends
    today = datetime.datetime.now()
    return today.weekday() >= 5  # Saturday = 5, Sunday = 6

def check_new_orders():
    """Check for new orders and order status changes, then send alerts"""
    global processed_orders, order_status_tracker
    
    # Skip if not in market hours
    if not is_market_hours():
        return
    
    try:
        # Get current orders
        current_orders = kite.orders()
        
        # Check for new orders and status changes
        for order in current_orders:
            order_id = order.get('order_id')
            current_status = order.get('status')
            
            if not order_id:
                continue
                
            # Check if this is a completely new order
            if order_id not in processed_orders:
                processed_orders.add(order_id)
                order_status_tracker[order_id] = current_status
                
                # Send alert for the new order
                alert_message = format_order_alert(order)
                send_telegram_message(alert_message)
                
                logging.info(f"Alert sent for new order: {order_id} - Status: {current_status}")
            
            # Check for status changes on existing orders
            elif order_id in order_status_tracker:
                previous_status = order_status_tracker[order_id]
                
                # If status has changed
                if current_status != previous_status:
                    # Update the status tracker
                    order_status_tracker[order_id] = current_status
                    
                    # Send status change alert
                    status_alert_message = format_status_change_alert(order, previous_status, current_status)
                    send_telegram_message(status_alert_message)
                    
                    logging.info(f"Status change alert sent for order: {order_id} - {previous_status} → {current_status}")
    
    except Exception as e:
        logging.error(f"Error checking orders: {str(e)}")

def start_order_monitoring():
    """Start the order monitoring service"""
    global order_status_tracker
    
    logging.info("Starting order monitoring service...")
    
    # Initialize with existing orders to avoid sending alerts for old orders
    try:
        existing_orders = kite.orders()
        for order in existing_orders:
            order_id = order.get('order_id')
            order_status = order.get('status')
            if order_id:
                processed_orders.add(order_id)
                order_status_tracker[order_id] = order_status
        logging.info(f"Initialized with {len(processed_orders)} existing orders")
    except Exception as e:
        logging.error(f"Error initializing existing orders: {str(e)}")
    
    print(f"Initialized with {len(processed_orders)} existing orders")
    
    # Send initial status message
    if is_market_hours():
        send_telegram_message("🟢 Market is OPEN - Order monitoring started!\n📋 Monitoring: New Orders + Status Changes")
    else:
        send_telegram_message("🔴 Market is CLOSED - Monitoring will start when market opens\n📋 Will monitor: New Orders + Status Changes")
    
    # Start monitoring loop
    while True:
        try:
            current_time = datetime.datetime.now().strftime("%H:%M:%S")
            
            if is_market_hours():
                # Market is open - monitor orders
                check_new_orders()
                # Print status every minute (20 cycles of 3 seconds)
                if datetime.datetime.now().second % 60 == 0:
                    print(f"[{current_time}] 🟢 Monitoring active - Market hours (Orders: {len(processed_orders)})")
            else:
                # Market is closed - just wait
                if datetime.datetime.now().second % 300 == 0:  # Print every 5 minutes
                    print(f"[{current_time}] 🔴 Market closed - Waiting for market hours (9:15 AM - 3:30 PM)")
                    
            time.sleep(3)  # Check every 3 seconds
            
        except KeyboardInterrupt:
            logging.info("Order monitoring stopped by user")
            send_telegram_message("⏹️ Order monitoring stopped by user")
            break
        except Exception as e:
            logging.error(f"Error in monitoring loop: {str(e)}")
            time.sleep(3)

def start_monitoring_thread():
    """Start order monitoring in a separate thread"""
    monitoring_thread = threading.Thread(target=start_order_monitoring, daemon=True)
    monitoring_thread.start()
    logging.info("Order monitoring thread started")
    return monitoring_thread

# Uncomment the line below to start monitoring automatically
# start_monitoring_thread()
start_order_monitoring()

# Test the system with a sample message (commented out)
# send_telegram_message("🤖 Zerodha Order Alert System Initialized Successfully!")

# kite.orders()